import React, { useMemo } from "react";
import useGameStore from "../../store/useGameStore";
import { gridToWorld } from "../../lib/maze";
import { generateTexture } from "../../lib/textures";

const WALL_SIZE = 2.5;
const WALL_HEIGHT = 4;

const Maze = () => {
  const mazeData = useGameStore((state) => state.mazeData);

  const wallTexture = useMemo(
    () => generateTexture(64, "#1a1a1a", "#2a2a2a"),
    []
  );

  const floorTexture = useMemo(() => {
    if (!mazeData) return null;
    const texture = generateTexture(128, "#2d2d2d", "#3d3d3d");
    texture.repeat.set(mazeData.mazeSize, mazeData.mazeSize);
    return texture;
  }, [mazeData]);

  if (!mazeData) return null;

  const { mazeMap, mazeSize, doorPos } = mazeData;

  return (
    <>
      {mazeMap.map((row, z) =>
        row.map((cell, x) => {
          if (cell === 1) {
            return (
              <mesh
                key={`${x}-${z}`}
                position={[
                  gridToWorld(x, mazeSize, WALL_SIZE),
                  WALL_HEIGHT / 2,
                  gridToWorld(z, mazeSize, WALL_SIZE),
                ]}
                castShadow
                receiveShadow
              >
                <boxGeometry args={[WALL_SIZE, WALL_HEIGHT, WALL_SIZE]} />
                <meshStandardMaterial map={wallTexture} roughness={0.9} />
              </mesh>
            );
          }
          return null;
        })
      )}
      <mesh rotation={[-Math.PI / 2, 0, 0]} receiveShadow>
        <planeGeometry args={[mazeSize * WALL_SIZE, mazeSize * WALL_SIZE]} />
        <meshStandardMaterial map={floorTexture} roughness={0.8} />
      </mesh>
      <mesh
        position={[
          gridToWorld(doorPos.x, mazeSize, WALL_SIZE),
          1.5,
          gridToWorld(doorPos.z, mazeSize, WALL_SIZE),
        ]}
      >
        <boxGeometry args={[WALL_SIZE * 0.9, 3, 0.2]} />
        <meshStandardMaterial color={0x8b4513} />
      </mesh>
    </>
  );
};

export default Maze;
