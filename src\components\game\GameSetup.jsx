import { useEffect } from "react";
import * as THREE from "three";
import useGameStore from "../../store/useGameStore";
import { generateMaze, gridToWorld } from "../../lib/maze";

const WALL_SIZE = 2.5;
const WALL_HEIGHT = 4;

const GameSetup = () => {
  const {
    level,
    actions: { setMazeData, setCollidables },
  } = useGameStore();

  useEffect(() => {
    const mazeSize = 13 + level * 2;
    const mazeMap = generateMaze(mazeSize, mazeSize);

    const emptySpaces = [];
    for (let z = 0; z < mazeSize; z++) {
      for (let x = 0; x < mazeSize; x++) {
        if (mazeMap[z][x] === 0) {
          emptySpaces.push({ x, z });
        }
      }
    }

    for (let i = emptySpaces.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [emptySpaces[i], emptySpaces[j]] = [emptySpaces[j], emptySpaces[i]];
    }

    const playerStartPos = emptySpaces.pop();
    const doorPos = emptySpaces.pop();
    const keyPos = emptySpaces.pop();
    const enemyStartPos = emptySpaces.pop();

    const numCoins = 3 + Math.floor(Math.random() * 3);
    const coinPositions = [];
    for (let i = 0; i < numCoins && emptySpaces.length > 0; i++) {
      coinPositions.push(emptySpaces.pop());
    }

    setMazeData({
      mazeMap,
      mazeSize,
      playerStartPos,
      doorPos,
      keyPos,
      enemyStartPos,
      coinPositions,
    });

    const wallBoxes = [];
    mazeMap.forEach((row, z) => {
      row.forEach((cell, x) => {
        if (cell === 1) {
          const wallPos = new THREE.Vector3(
            gridToWorld(x, mazeSize, WALL_SIZE),
            WALL_HEIGHT / 2,
            gridToWorld(z, mazeSize, WALL_SIZE)
          );
          const wallBox = new THREE.Box3().setFromCenterAndSize(
            wallPos,
            new THREE.Vector3(WALL_SIZE, WALL_HEIGHT, WALL_SIZE)
          );
          wallBoxes.push(wallBox);
        }
      });
    });
    setCollidables(wallBoxes);
  }, [level, setMazeData, setCollidables]);

  return null;
};

export default GameSetup;
