import { useState, useEffect } from "react";

const usePlayerControls = () => {
  const [movement, setMovement] = useState({
    moveForward: false,
    moveBackward: false,
    moveLeft: false,
    moveRight: false,
    sprint: false,
  });

  useEffect(() => {
    const handleKeyDown = (e) => {
      switch (e.code) {
        case "KeyW":
          setMovement((m) => ({ ...m, moveForward: true }));
          break;
        case "KeyS":
          setMovement((m) => ({ ...m, moveBackward: true }));
          break;
        case "KeyA":
          setMovement((m) => ({ ...m, moveLeft: true }));
          break;
        case "KeyD":
          setMovement((m) => ({ ...m, moveRight: true }));
          break;
        case "ShiftLeft":
          setMovement((m) => ({ ...m, sprint: true }));
          break;
        default:
          break;
      }
    };

    const handleKeyUp = (e) => {
      switch (e.code) {
        case "KeyW":
          setMovement((m) => ({ ...m, moveForward: false }));
          break;
        case "KeyS":
          setMovement((m) => ({ ...m, moveBackward: false }));
          break;
        case "KeyA":
          setMovement((m) => ({ ...m, moveLeft: false }));
          break;
        case "KeyD":
          setMovement((m) => ({ ...m, moveRight: false }));
          break;
        case "ShiftLeft":
          setMovement((m) => ({ ...m, sprint: false }));
          break;
        default:
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("keyup", handleKeyUp);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  return movement;
};

export default usePlayerControls;
