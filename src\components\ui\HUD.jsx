import React from "react";
import useGameStore from "../../store/useGameStore";

const HUD = () => {
  const {
    level,
    money,
    stamina,
    flashlightBattery,
    spareBatteries,
    ammo,
    hasWeapon,
  } = useGameStore();

  return (
    <div
      id="ui-container"
      className="fixed inset-0 pointer-events-none z-10 flex flex-col justify-between p-5 text-white"
    >
      <div className="flex justify-between items-start">
        <div
          id="game-stats"
          className="bg-black/50 p-3 rounded-lg text-lg shadow-lg"
        >
          <div id="level-display">Level: {level}</div>
          <div id="money-display" className="flex items-center">
            <svg
              className="w-5 h-5 mr-2 text-yellow-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"></path>
            </svg>
            Coins: {money}
          </div>
        </div>
        <div
          id="minimap-container"
          className="border-2 border-white/30 bg-black/70 rounded-lg shadow-2xl"
        >
          <canvas id="minimap-canvas"></canvas>
        </div>
      </div>

      <div className="absolute top-1/4 left-1/2 -translate-x-1/2 w-full max-w-md">
        <div
          id="key-message"
          className="hidden bg-green-900/80 border border-green-500 text-center p-4 rounded-lg shadow-2xl"
        >
          Anahtar Bulundu! Çıkışı ara.
        </div>
        <div
          id="door-locked-message"
          className="hidden bg-red-900/80 border border-red-500 text-center p-4 rounded-lg shadow-2xl"
        >
          Kapı Kilitli. Anahtarı bulmalısın.
        </div>
      </div>

      <div
        id="crosshair"
        className="w-3 h-3 border-2 border-white rounded-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
      ></div>

      <div className="flex justify-between items-end">
        <div
          id="stamina-container"
          className="w-52 h-10 bg-black/50 p-1 rounded-lg border border-white/30"
        >
          <div className="w-full h-full bg-gray-700 rounded">
            <div
              id="stamina-bar"
              style={{ width: `${stamina}%` }}
              className="h-full bg-green-500 rounded transition-all duration-200"
            ></div>
          </div>
        </div>

        <div
          id="whisper-message"
          className="text-2xl text-red-500 text-center text-flicker opacity-0 transition-opacity duration-500"
          style={{ textShadow: "0 0 8px #ff0000" }}
        ></div>

        <div className="flex flex-col items-end space-y-3">
          {hasWeapon && (
            <div
              id="ammo-display"
              className="text-xl bg-black/50 px-3 py-1 rounded-md"
            >{`Mermi: ${ammo}/5`}</div>
          )}
          <div
            id="spare-batteries-ui"
            className="text-2xl bg-black/50 px-3 py-1 rounded-md flex items-center"
          >
            <svg
              className="w-6 h-6 mr-2 text-yellow-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M11.3 1.046A1 1 0 0112 2v1.268l-2.268 2.268a2.5 2.5 0 11-3.536 3.536L2.93 12.268A1 1 0 012 11.3V10a1 1 0 011-1h1.268l2.268-2.268a2.5 2.5 0 113.536-3.536L12.268 1H11.3zM4 14a1 1 0 011-1h1.268l.932.932a1 1 0 01-1.414 1.414L4 14.732V14zm2.586-1.414a1 1 0 011.414 0L14.732 18H15a1 1 0 011 1v1.268l-1.046.26a1 1 0 01-1.222-1.222L13 16.586l-6.414-6.414a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span id="spare-batteries-count">{spareBatteries}</span>
          </div>
          <div
            id="battery-container"
            className="w-52 h-10 bg-black/50 p-1 rounded-lg border border-white/30"
          >
            <div className="w-full h-full bg-gray-700 rounded">
              <div
                id="battery-bar"
                style={{ width: `${flashlightBattery}%` }}
                className="h-full bg-yellow-400 rounded transition-all duration-200"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HUD;
