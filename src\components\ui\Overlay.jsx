import React from "react";
import useGameStore from "../../store/useGameStore";

const Overlay = () => {
  const {
    isGameOver,
    gameActive,
    isShopOpen,
    actions: { setGameActive, reset },
  } = useGameStore();

  const handleStartClick = () => {
    document.body.requestPointerLock();
    setGameActive(true);
  };

  const handleRestartClick = () => {
    reset();
    document.body.requestPointerLock();
    setGameActive(true);
  };

  if (gameActive || isShopOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex flex-col justify-center items-center pointer-events-auto cursor-pointer z-20">
      {!isGameOver ? (
        <div id="start-screen" onClick={handleStartClick}>
          <h1
            id="start-title"
            className="text-6xl font-bold tracking-widest text-red-700 text-flicker"
          >
            GÖZCÜLER
          </h1>
          <p id="start-subtitle" className="text-xl mt-4">
            <PERSON>ir sonraki seviyeye geçmek için anahtarı bul.
          </p>
          <p id="start-warning" className="text-lg mt-2 text-gray-400">
            Sadece ışıktan korkarlar.
          </p>
          <p
            id="start-prompt"
            className="mt-10 text-2xl font-bold bg-white/10 px-4 py-2 rounded-lg border border-white/30 animate-pulse"
          >
            Başlamak için Tıkla
          </p>
          <p className="absolute bottom-5 text-gray-400">
            (W,A,S,D: Hareket | Shift: Koş | F: Fener | R: Batarya | E:
            Etkileşim/Saklan | TAB: Dükkan)
          </p>
        </div>
      ) : (
        <div id="game-over-screen" onClick={handleRestartClick}>
          <h1 className="text-7xl font-bold text-red-700">Oyun Bitti</h1>
          <p id="game-over-taunt" className="text-2xl mt-4">
            Kaçamadın.
          </p>
          <p className="mt-10 text-2xl font-bold bg-white/10 px-4 py-2 rounded-lg border border-white/30 animate-pulse">
            Yeniden Başlamak için Tıkla
          </p>
        </div>
      )}
    </div>
  );
};

export default Overlay;
