import { use<PERSON>rame, useThree } from "@react-three/fiber";
import { useEffect, useRef } from "react";
import * as THREE from "three";
import useGameStore from "../../store/useGameStore";
import usePlayerControls from "../../hooks/usePlayerControls";
import { gridToWorld } from "../../lib/maze";

const PLAYER_BODY_SIZE = new THREE.Vector3(0.7, 1.8, 0.7);

const Player = () => {
  const { camera, scene } = useThree();
  const {
    isHiding,
    stamina,
    mazeData,
    collidables,
    actions: { setStamina },
  } = useGameStore();
  const { moveForward, moveBackward, moveLeft, moveRight, sprint } =
    usePlayerControls();
  const velocity = useRef(new THREE.Vector3());
  const playerRef = useRef(new THREE.Object3D());
  const playerCollider = useRef(new THREE.Box3());

  useEffect(() => {
    // Set camera height relative to player
    camera.position.set(0, 1.8, 0);

    // Add camera to player object for movement
    playerRef.current.add(camera);
    scene.add(playerRef.current);

    if (mazeData) {
      // Position player at start position
      const startX = gridToWorld(
        mazeData.playerStartPos.x,
        mazeData.mazeSize,
        2.5
      );
      const startZ = gridToWorld(
        mazeData.playerStartPos.z,
        mazeData.mazeSize,
        2.5
      );

      playerRef.current.position.set(startX, PLAYER_BODY_SIZE.y / 2, startZ);
      console.log("Player positioned at:", startX, startZ);
    }
  }, [camera, scene, mazeData]);

  const checkPlayerCollisions = () => {
    if (!playerRef.current) return;
    playerCollider.current.setFromCenterAndSize(
      playerRef.current.position,
      PLAYER_BODY_SIZE
    );

    for (const box of collidables) {
      if (playerCollider.current.intersectsBox(box)) {
        const intersection = new THREE.Box3();
        intersection.copy(playerCollider.current).intersect(box);
        const penetration = new THREE.Vector3();
        penetration.x = intersection.max.x - intersection.min.x;
        penetration.z = intersection.max.z - intersection.min.z;

        const playerCenter = new THREE.Vector3();
        playerCollider.current.getCenter(playerCenter);
        const boxCenter = new THREE.Vector3();
        box.getCenter(boxCenter);

        const toPlayer = playerCenter.sub(boxCenter);

        if (penetration.x < penetration.z) {
          playerRef.current.position.x +=
            toPlayer.x > 0 ? penetration.x : -penetration.x;
        } else {
          playerRef.current.position.z +=
            toPlayer.z > 0 ? penetration.z : -penetration.z;
        }
      }
    }
  };

  useFrame((_, delta) => {
    if (isHiding || !mazeData) {
      velocity.current.set(0, 0, 0);
      return;
    }

    const speed = sprint && stamina > 0 ? 3.5 * 1.8 : 3.5;
    const direction = new THREE.Vector3();

    // Doğru yön atamaları: W/S = ileri/geri (z ekseni), A/D = sol/sağ (x ekseni)
    direction.z = Number(moveForward) - Number(moveBackward); // Z ekseni düz
    direction.x = Number(moveRight) - Number(moveLeft);

    // Debug: direction değerlerini kontrol et
    if (direction.z !== 0 || direction.x !== 0) {
      console.log("Direction:", {
        z: direction.z,
        x: direction.x,
        moveForward,
        moveBackward,
        moveLeft,
        moveRight,
      });
    }

    direction.normalize();

    if (sprint && direction.length() > 0) {
      setStamina(Math.max(0, stamina - 35 * delta));
    } else if (stamina < 100) {
      setStamina(Math.min(100, stamina + 15 * delta));
    }

    // Kamera yönlendirmesini doğrudan kullan
    const cameraDirection = new THREE.Vector3();
    camera.getWorldDirection(cameraDirection);

    // Forward vektörü (kameranın baktığı yön, Y bileşenini sıfırla)
    const forward = new THREE.Vector3(
      -cameraDirection.x,
      0,
      -cameraDirection.z
    ).normalize();

    // Right vektörü (forward'a dik)
    const right = new THREE.Vector3()
      .crossVectors(forward, new THREE.Vector3(0, 1, 0))
      .normalize();

    // Hareket yönünü hesapla
    const moveDirection = new THREE.Vector3();
    moveDirection.addScaledVector(forward, direction.z);
    moveDirection.addScaledVector(right, direction.x);

    velocity.current.x = moveDirection.x * speed;
    velocity.current.z = moveDirection.z * speed;

    playerRef.current.position.x += velocity.current.x * delta;
    checkPlayerCollisions();
    playerRef.current.position.z += velocity.current.z * delta;
    checkPlayerCollisions();
  });

  return null;
};

export default Player;
