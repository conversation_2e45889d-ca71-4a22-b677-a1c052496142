import { use<PERSON>rame, useThree } from "@react-three/fiber";
import { useEffect, useRef } from "react";
import * as THREE from "three";
import useGameStore from "../../store/useGameStore";
import usePlayerControls from "../../hooks/usePlayerControls";

const Player = () => {
  const { camera, scene } = useThree();
  const {
    isHiding,
    stamina,
    actions: { setStamina },
  } = useGameStore();
  const { moveForward, moveBackward, moveLeft, moveRight, sprint } =
    usePlayerControls();
  const velocity = useRef(new THREE.Vector3());
  const playerRef = useRef(new THREE.Object3D());

  useEffect(() => {
    camera.position.y = 1.8 * 0.4;
    playerRef.current.add(camera);
    scene.add(playerRef.current);
  }, [camera, scene]);

  useFrame((_, delta) => {
    if (isHiding) {
      velocity.current.set(0, 0, 0);
      return;
    }

    const speed = sprint && stamina > 0 ? 3.5 * 1.8 : 3.5;
    const direction = new THREE.Vector3();
    direction.z = Number(moveForward) - Number(moveBackward);
    direction.x = Number(moveRight) - Number(moveLeft);
    direction.normalize();

    if (sprint && direction.length() > 0) {
      setStamina(Math.max(0, stamina - 35 * delta));
    } else if (stamina < 100) {
      setStamina(Math.min(100, stamina + 15 * delta));
    }

    velocity.current.x = direction.x * speed;
    velocity.current.z = direction.z * speed;

    playerRef.current.translateX(velocity.current.x * delta);
    playerRef.current.translateZ(velocity.current.z * delta);
  });

  return null;
};

export default Player;
