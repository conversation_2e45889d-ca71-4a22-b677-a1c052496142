{"name": "escape-from-maze", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@tailwindcss/vite": "^4.1.11", "@types/three": "^0.177.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11", "three": "^0.177.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}