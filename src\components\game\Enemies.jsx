import { useRef, useState, useEffect } from "react";
import { use<PERSON>rame, useThree } from "@react-three/fiber";
import * as THREE from "three";
import useGameStore from "../../store/useGameStore";
import { gridToWorld } from "../../lib/maze";

const WALL_SIZE = 2.5;
const ENEMY_SIZE = new THREE.Vector3(0.8, 1.5, 0.8);
const ENEMY_SPEED = 2.0;
const DETECTION_RANGE = 8.0;
const FLASHLIGHT_FEAR_RANGE = 6.0;

const Enemy = ({ startPosition, onPlayerCaught }) => {
  const { camera } = useThree();
  const { mazeData, collidables, flashlightBattery, isHiding } = useGameStore();

  const enemyRef = useRef();
  const enemyCollider = useRef(new THREE.Box3());

  const [isChasing, setIsChasing] = useState(false);
  const [isFleeing, setIsFleeing] = useState(false);
  const lastPlayerPos = useRef(new THREE.Vector3());
  const wanderTarget = useRef(new THREE.Vector3());
  const wanderTimer = useRef(0);

  useEffect(() => {
    if (enemyRef.current && startPosition) {
      enemyRef.current.position.set(
        gridToWorld(startPosition.x, mazeData.mazeSize, WALL_SIZE),
        ENEMY_SIZE.y / 2,
        gridToWorld(startPosition.z, mazeData.mazeSize, WALL_SIZE)
      );
    }
  }, [startPosition, mazeData]);

  const checkEnemyCollisions = (newPosition) => {
    if (!enemyRef.current) return false;

    enemyCollider.current.setFromCenterAndSize(newPosition, ENEMY_SIZE);

    for (const box of collidables) {
      if (enemyCollider.current.intersectsBox(box)) {
        return true;
      }
    }
    return false;
  };

  const getRandomWanderTarget = () => {
    const range = 5;
    const currentPos = enemyRef.current.position;
    const newTarget = new THREE.Vector3(
      currentPos.x + (Math.random() - 0.5) * range,
      currentPos.y,
      currentPos.z + (Math.random() - 0.5) * range
    );
    return newTarget;
  };

  const isPlayerInFlashlightBeam = (playerPos, enemyPos) => {
    if (flashlightBattery <= 0) return false;

    // Simple flashlight detection - check if enemy is in front of player
    const playerToEnemy = new THREE.Vector3().subVectors(enemyPos, playerPos);
    const playerForward = new THREE.Vector3(0, 0, -1);

    if (camera.parent) {
      playerForward.applyQuaternion(camera.parent.quaternion);
    }

    const distance = playerToEnemy.length();
    if (distance > FLASHLIGHT_FEAR_RANGE) return false;

    const angle = playerForward.angleTo(playerToEnemy);
    return angle < Math.PI / 3; // 60 degree cone
  };

  useFrame((_, delta) => {
    if (!enemyRef.current || !camera.parent || !mazeData) return;

    const playerPos = camera.parent.position;
    const enemyPos = enemyRef.current.position;
    const distanceToPlayer = playerPos.distanceTo(enemyPos);

    // Check if player is caught
    if (distanceToPlayer < 1.5 && !isHiding) {
      onPlayerCaught();
      return;
    }

    // Check if player is in flashlight beam
    const inFlashlight = isPlayerInFlashlightBeam(playerPos, enemyPos);

    if (inFlashlight) {
      setIsFleeing(true);
      setIsChasing(false);
    } else if (distanceToPlayer < DETECTION_RANGE && !isHiding) {
      setIsChasing(true);
      setIsFleeing(false);
      lastPlayerPos.current.copy(playerPos);
    } else if (isChasing && distanceToPlayer > DETECTION_RANGE * 1.5) {
      setIsChasing(false);
    }

    let targetPosition = new THREE.Vector3();
    let speed = ENEMY_SPEED;

    if (isFleeing && inFlashlight) {
      // Flee from player
      const fleeDirection = new THREE.Vector3()
        .subVectors(enemyPos, playerPos)
        .normalize();
      targetPosition
        .copy(enemyPos)
        .add(fleeDirection.multiplyScalar(speed * delta * 2));
      speed *= 1.5; // Flee faster
    } else if (isChasing) {
      // Chase player or last known position
      targetPosition.copy(lastPlayerPos.current);
    } else {
      // Wander behavior
      wanderTimer.current -= delta;
      if (
        wanderTimer.current <= 0 ||
        enemyPos.distanceTo(wanderTarget.current) < 1
      ) {
        wanderTarget.current = getRandomWanderTarget();
        wanderTimer.current = 3 + Math.random() * 4; // 3-7 seconds
      }
      targetPosition.copy(wanderTarget.current);
      speed *= 0.5; // Wander slower
    }

    // Move towards target
    const direction = new THREE.Vector3().subVectors(targetPosition, enemyPos);
    if (direction.length() > 0.1) {
      direction.normalize();
      const newPosition = enemyPos
        .clone()
        .add(direction.multiplyScalar(speed * delta));

      // Check collisions before moving
      if (!checkEnemyCollisions(newPosition)) {
        enemyRef.current.position.copy(newPosition);
      } else {
        // If collision, try a different direction
        if (!isChasing && !isFleeing) {
          wanderTarget.current = getRandomWanderTarget();
        }
      }
    }

    // Reset fleeing if not in flashlight anymore
    if (isFleeing && !inFlashlight) {
      setIsFleeing(false);
    }
  });

  return (
    <mesh ref={enemyRef} castShadow>
      <boxGeometry args={[ENEMY_SIZE.x, ENEMY_SIZE.y, ENEMY_SIZE.z]} />
      <meshStandardMaterial
        color={isChasing ? 0xff0000 : isFleeing ? 0x0000ff : 0x333333}
        emissive={isChasing ? 0x440000 : 0x000000}
        emissiveIntensity={isChasing ? 0.3 : 0}
      />
    </mesh>
  );
};

const Enemies = () => {
  const {
    mazeData,
    actions: { setIsGameOver },
  } = useGameStore();

  const handlePlayerCaught = () => {
    setIsGameOver(true);
  };

  if (!mazeData) return null;

  return (
    <Enemy
      startPosition={mazeData.enemyStartPos}
      onPlayerCaught={handlePlayerCaught}
    />
  );
};

export default Enemies;
