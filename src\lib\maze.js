export function generateMaze(width, height) {
  const map = Array(height)
    .fill(null)
    .map(() => Array(width).fill(1));
  const stack = [];
  const startX = 1,
    startY = 1;
  map[startY][startX] = 0;
  stack.push([startX, startY]);

  while (stack.length > 0) {
    const [cx, cy] = stack[stack.length - 1];
    const neighbors = [];
    if (cx > 1 && map[cy][cx - 2] === 1)
      neighbors.push([cx - 2, cy, cx - 1, cy]);
    if (cx < width - 2 && map[cy][cx + 2] === 1)
      neighbors.push([cx + 2, cy, cx + 1, cy]);
    if (cy > 1 && map[cy - 2][cx] === 1)
      neighbors.push([cx, cy - 2, cx, cy - 1]);
    if (cy < height - 2 && map[cy + 2][cx] === 1)
      neighbors.push([cx, cy + 2, cx, cy + 1]);

    if (neighbors.length > 0) {
      const [nx, ny, px, py] =
        neighbors[Math.floor(Math.random() * neighbors.length)];
      map[ny][nx] = 0;
      map[py][px] = 0;
      stack.push([nx, ny]);
    } else {
      stack.pop();
    }
  }
  return map;
}

export function gridToWorld(coord, max, wallSize) {
  return (coord - max / 2 + 0.5) * wallSize;
}
