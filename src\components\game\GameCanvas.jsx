import React, { Suspense } from "react";
import { Canvas } from "@react-three/fiber";
import { PointerLockControls } from "@react-three/drei";
import useGameStore from "../../store/useGameStore";
import Player from "./Player";
import Maze from "./Maze";
import Enemies from "./Enemies";
import Collectibles from "./Collectibles";

const GameCanvas = () => {
  const { gameActive, isShopOpen } = useGameStore();

  return (
    <Canvas shadows camera={{ fov: 75, position: [0, 1.8 * 0.4, 0] }}>
      <Suspense fallback={null}>
        <ambientLight intensity={0.4} />
        <hemisphereLight intensity={0.3} groundColor="black" />
        <fog attach="fog" args={["#000", 0, 0.04]} />
        <Maze />
        <Player />
        <Enemies />
        <Collectibles />
        {gameActive && !isShopOpen && <PointerLockControls />}
      </Suspense>
    </Canvas>
  );
};

export default GameCanvas;
