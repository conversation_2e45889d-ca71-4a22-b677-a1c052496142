import React, { Suspense } from "react";
import { Canvas } from "@react-three/fiber";
import { PointerLockControls } from "@react-three/drei";
import useGameStore from "../../store/useGameStore";
import Player from "./Player";
import Maze from "./Maze";
import Enemies from "./Enemies";
import Collectibles from "./Collectibles";
import Flashlight from "./Flashlight";

const GameCanvas = () => {
  const { gameActive, isShopOpen } = useGameStore();

  return (
    <div style={{ width: "100vw", height: "100vh" }}>
      <Canvas
        shadows
        camera={{ fov: 75, position: [0, 1.8, 0] }}
        style={{ width: "100%", height: "100%" }}
      >
        <Suspense fallback={null}>
          <ambientLight intensity={0.8} />
          <hemisphereLight intensity={0.6} groundColor="#444" />
          <directionalLight
            position={[10, 10, 5]}
            intensity={0.5}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
          />
          <fog attach="fog" args={["#000", 15, 60]} />
          <Maze />
          <Player />
          <Enemies />
          <Collectibles />
          <Flashlight />
          {gameActive && !isShopOpen && (
            <PointerLockControls makeDefault selector="#root" />
          )}
        </Suspense>
      </Canvas>
    </div>
  );
};

export default GameCanvas;
