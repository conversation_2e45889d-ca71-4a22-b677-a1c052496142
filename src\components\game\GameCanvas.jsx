import React, { Suspense } from "react";
import { Canvas } from "@react-three/fiber";
import { PointerLockControls } from "@react-three/drei";
import useGameStore from "../../store/useGameStore";
import Player from "./Player";
import Maze from "./Maze";
import Enemies from "./Enemies";
import Collectibles from "./Collectibles";
import Flashlight from "./Flashlight";

const GameCanvas = () => {
  const { gameActive, isShopOpen } = useGameStore();

  return (
    <Canvas shadows camera={{ fov: 75, position: [0, 1.8, 0] }}>
      <Suspense fallback={null}>
        <ambientLight intensity={0.6} />
        <hemisphereLight intensity={0.5} groundColor="#222" />
        <directionalLight position={[10, 10, 5]} intensity={0.3} />
        <fog attach="fog" args={["#000", 10, 50]} />
        <Maze />
        <Player />
        <Enemies />
        <Collectibles />
        <Flashlight />
        {gameActive && !isShopOpen && <PointerLockControls />}
      </Suspense>
    </Canvas>
  );
};

export default GameCanvas;
