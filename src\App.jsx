import React from "react";
import GameCanvas from "./components/game/GameCanvas";
import Overlay from "./components/ui/Overlay";
import HUD from "./components/ui/HUD";
import Shop from "./components/ui/Shop";
import useGameStore from "./store/useGameStore";
import GameSetup from "./components/game/GameSetup";

function App() {
  const { isShopOpen, gameActive } = useGameStore();

  return (
    <>
      <GameSetup />
      <GameCanvas />
      <Overlay />
      {gameActive && <HUD />}
      {isShopOpen && <Shop />}
    </>
  );
}

export default App;
