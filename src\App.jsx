import React from "react";
import GameCanvas from "./components/game/GameCanvas";
import Overlay from "./components/ui/Overlay";
import HUD from "./components/ui/HUD";
import Shop from "./components/ui/Shop";
import useGameStore from "./store/useGameStore";

function App() {
  const { isShopOpen, gameActive } = useGameStore();

  return (
    <>
      <GameCanvas />
      <Overlay />
      {gameActive && <HUD />}
      {isShopOpen && <Shop />}
    </>
  );
}

export default App;
