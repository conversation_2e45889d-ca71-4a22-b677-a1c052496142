import React from "react";
import useGameStore from "../../store/useGameStore";

const Shop = () => {
  const {
    money,
    speedBoostLevel,
    maxFlashlightBattery,
    hasWeapon,
    flashlightDrainRate,
    actions: {
      setMoney,
      setSpeedBoostLevel,
      setMaxFlashlightBattery,
      setHasWeapon,
      setFlashlightDrainRate,
    },
  } = useGameStore();

  const purchaseUpgrade = (type) => {
    switch (type) {
      case "speed": {
        const speedCost = 100 + speedBoostLevel * 50;
        if (money >= speedCost) {
          setMoney(money - speedCost);
          setSpeedBoostLevel(speedBoostLevel + 1);
        }
        break;
      }
      case "battery": {
        if (money >= 150 && maxFlashlightBattery <= 100) {
          setMoney(money - 150);
          setMaxFlashlightBattery(150);
        }
        break;
      }
      case "weapon": {
        if (money >= 200 && !hasWeapon) {
          setMoney(money - 200);
          setHasWeapon(true);
        }
        break;
      }
      case "drain": {
        if (money >= 125 && flashlightDrainRate >= 3.0) {
          setMoney(money - 125);
          setFlashlightDrainRate(2.0);
        }
        break;
      }
      case "color": {
        if (money >= 75) {
          setMoney(money - 75);
          // Implement color change logic
        }
        break;
      }
      default:
        break;
    }
  };

  const shopItems = [
    {
      id: "speed",
      title: `Hız Artışı (Seviye ${speedBoostLevel + 1})`,
      desc: "Koşma hızını kalıcı artırır.",
      cost: 100 + speedBoostLevel * 50,
      disabled: money < 100 + speedBoostLevel * 50,
    },
    {
      id: "battery",
      title: "Pil Güçlendirme",
      desc: "Maksimum pil kapasitesini artırır.",
      cost: 150,
      disabled: money < 150 || maxFlashlightBattery > 100,
      bought: maxFlashlightBattery > 100,
    },
    {
      id: "weapon",
      title: "Şok Silahı",
      desc: "Düşmanı 10 sn sersemletir (5 Mermi).",
      cost: 200,
      disabled: money < 200 || hasWeapon,
      bought: hasWeapon,
    },
    {
      id: "drain",
      title: "Pil Verimliliği",
      desc: "Fenerin pil tüketimini azaltır.",
      cost: 125,
      disabled: money < 125 || flashlightDrainRate < 3.0,
      bought: flashlightDrainRate < 3.0,
    },
  ];

  return (
    <div
      id="shop-overlay"
      className="fixed inset-0 bg-black/80 backdrop-blur-sm flex justify-center items-center pointer-events-auto z-50"
    >
      <div
        id="shop-container"
        className="bg-gray-900/80 border-2 border-yellow-400/50 p-8 rounded-2xl w-full max-w-3xl text-center shadow-2xl shadow-yellow-500/10"
      >
        <h2 className="text-5xl font-bold mb-6 text-yellow-400">DÜKKAN</h2>
        <div
          id="shop-items"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {shopItems.map((item) => (
            <div
              key={item.id}
              className="bg-gray-800/70 p-4 rounded-lg border border-white/20 flex flex-col justify-between"
            >
              <div>
                <h3 className="text-xl font-bold text-yellow-300">
                  {item.title}
                </h3>
                <p className="text-gray-300 mt-1">{item.desc}</p>
              </div>
              <button
                onClick={() => purchaseUpgrade(item.id)}
                disabled={item.disabled || item.bought}
                className="mt-4 w-full bg-green-600 hover:bg-green-500 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded transition-colors"
              >
                {item.bought ? "Satın Alındı" : `Satın Al (${item.cost} Coins)`}
              </button>
            </div>
          ))}
        </div>
        <p className="mt-8 text-gray-400">(Kapatmak için TAB veya ESC)</p>
      </div>
    </div>
  );
};

export default Shop;
