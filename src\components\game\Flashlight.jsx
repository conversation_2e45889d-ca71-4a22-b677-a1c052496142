import { useRef, useState, useEffect } from "react";
import { useFrame, useThree } from "@react-three/fiber";
import * as THREE from "three";
import useGameStore from "../../store/useGameStore";

const Flashlight = () => {
  const { camera } = useThree();
  const {
    flashlightBattery,
    flashlightDrainRate,
    spareBatteries,
    maxFlashlightBattery,
    actions: { setFlashlightBattery, setSpareBatteries },
  } = useGameStore();

  const [isFlashlightOn, setIsFlashlightOn] = useState(false);
  const spotLightRef = useRef();
  const targetRef = useRef();

  useFrame((_, delta) => {
    if (!camera.parent || !spotLightRef.current || !targetRef.current) return;

    // Update flashlight position and direction
    const playerPos = camera.parent.position;
    const playerRotation = camera.parent.quaternion;

    // Position flashlight slightly in front of player
    const flashlightOffset = new THREE.Vector3(0, 0, -0.5);
    flashlightOffset.applyQuaternion(playerRotation);
    spotLightRef.current.position.copy(playerPos).add(flashlightOffset);

    // Point flashlight forward
    const forward = new THREE.Vector3(0, 0, -1);
    forward.applyQuaternion(playerRotation);
    targetRef.current.position.copy(playerPos).add(forward.multiplyScalar(10));

    // Drain battery when flashlight is on
    if (isFlashlightOn && flashlightBattery > 0) {
      setFlashlightBattery(
        Math.max(0, flashlightBattery - flashlightDrainRate * delta)
      );
    }

    // Turn off flashlight if battery is dead
    if (flashlightBattery <= 0 && isFlashlightOn) {
      setIsFlashlightOn(false);
    }

    // Update light intensity based on battery
    if (spotLightRef.current) {
      const batteryRatio = flashlightBattery / maxFlashlightBattery;
      spotLightRef.current.intensity =
        isFlashlightOn && flashlightBattery > 0
          ? Math.max(0.1, batteryRatio) * 2
          : 0;
    }
  });

  // Handle flashlight toggle
  const toggleFlashlight = () => {
    if (flashlightBattery > 0) {
      setIsFlashlightOn(!isFlashlightOn);
    }
  };

  // Handle battery replacement
  const replaceBattery = () => {
    if (spareBatteries > 0 && flashlightBattery < maxFlashlightBattery) {
      setSpareBatteries(spareBatteries - 1);
      setFlashlightBattery(maxFlashlightBattery);
    }
  };

  // Add event listeners for flashlight controls
  useEffect(() => {
    const handleKeyDown = (e) => {
      switch (e.code) {
        case "KeyF":
          toggleFlashlight();
          break;
        case "KeyR":
          replaceBattery();
          break;
        default:
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isFlashlightOn, flashlightBattery, spareBatteries, maxFlashlightBattery]);

  return (
    <>
      <spotLight
        ref={spotLightRef}
        target={targetRef.current}
        intensity={0}
        angle={Math.PI / 6}
        penumbra={0.3}
        distance={15}
        decay={2}
        color={0xffffaa}
        castShadow
        shadow-mapSize-width={1024}
        shadow-mapSize-height={1024}
        shadow-camera-near={0.1}
        shadow-camera-far={20}
      />
      <object3D ref={targetRef} />
    </>
  );
};

export default Flashlight;
