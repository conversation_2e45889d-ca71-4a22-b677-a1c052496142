import { useRef, useState } from "react";
import { use<PERSON>rame, useThree } from "@react-three/fiber";
import * as THREE from "three";
import useGameStore from "../../store/useGameStore";
import { gridToWorld } from "../../lib/maze";

const WALL_SIZE = 2.5;

const Key = ({ position }) => (
  <mesh position={position}>
    <torusGeometry args={[0.15, 0.05, 8, 24]} />
    <meshStandardMaterial
      color={0xffd700}
      metalness={1}
      roughness={0.2}
      emissive={0xffd700}
      emissiveIntensity={0.5}
    />
  </mesh>
);

const Coin = ({ position }) => (
  <mesh position={position} rotation={[Math.PI / 2, 0, 0]}>
    <cylinderGeometry args={[0.2, 0.2, 0.05, 12]} />
    <meshStandardMaterial
      color={0xffd700}
      metalness={0.8}
      roughness={0.3}
      emissive={0xdaa520}
      emissiveIntensity={0.4}
    />
  </mesh>
);

const Collectibles = () => {
  const { camera } = useThree();
  const {
    mazeData,
    hasKey,
    money,
    actions: { setHasKey, setMoney },
  } = useGameStore();
  const [collectedCoins, setCollectedCoins] = useState(new Set());
  const playerPos = useRef(new THREE.Vector3());

  useFrame(() => {
    if (!mazeData || !camera.parent) return;

    // Get player position from camera parent (the player object)
    playerPos.current.copy(camera.parent.position);

    // Check key collection
    if (!hasKey) {
      const keyWorldPos = new THREE.Vector3(
        gridToWorld(mazeData.keyPos.x, mazeData.mazeSize, WALL_SIZE),
        0.5,
        gridToWorld(mazeData.keyPos.z, mazeData.mazeSize, WALL_SIZE)
      );

      if (playerPos.current.distanceTo(keyWorldPos) < 1.0) {
        setHasKey(true);
      }
    }

    // Check coin collection
    mazeData.coinPositions.forEach((pos, i) => {
      if (collectedCoins.has(i)) return;

      const coinWorldPos = new THREE.Vector3(
        gridToWorld(pos.x, mazeData.mazeSize, WALL_SIZE),
        0.3,
        gridToWorld(pos.z, mazeData.mazeSize, WALL_SIZE)
      );

      if (playerPos.current.distanceTo(coinWorldPos) < 1.0) {
        setCollectedCoins((prev) => new Set([...prev, i]));
        setMoney(money + 10);
      }
    });
  });

  if (!mazeData) return null;

  const { mazeSize, keyPos, coinPositions } = mazeData;

  const keyWorldPos = [
    gridToWorld(keyPos.x, mazeSize, WALL_SIZE),
    0.5,
    gridToWorld(keyPos.z, mazeSize, WALL_SIZE),
  ];

  return (
    <>
      {!hasKey && <Key position={keyWorldPos} />}
      {coinPositions.map((pos, i) => {
        if (collectedCoins.has(i)) return null;
        return (
          <Coin
            key={i}
            position={[
              gridToWorld(pos.x, mazeSize, WALL_SIZE),
              0.3,
              gridToWorld(pos.z, mazeSize, WALL_SIZE),
            ]}
          />
        );
      })}
    </>
  );
};

export default Collectibles;
