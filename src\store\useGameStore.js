import { create } from "zustand";

const useGameStore = create((set) => ({
  level: 1,
  money: 0,
  hasKey: false,
  gameActive: true,
  isGameOver: false,
  isShopOpen: false,
  isHiding: false,
  stamina: 100,
  flashlightBattery: 100,
  spareBatteries: 1,
  hasWeapon: false,
  ammo: 0,
  speedBoostLevel: 0,
  maxFlashlightBattery: 100,
  flashlightDrainRate: 3.0,
  mazeData: null,
  collidables: [],

  actions: {
    setLevel: (level) => set({ level }),
    setMoney: (money) => set({ money }),
    setHasKey: (hasKey) => set({ hasKey }),
    setGameActive: (gameActive) => set({ gameActive }),
    setIsGameOver: (isGameOver) => set({ isGameOver }),
    setIsShopOpen: (isShopOpen) => set({ isShopOpen }),
    setIsHiding: (isHiding) => set({ isHiding }),
    setStamina: (stamina) => set({ stamina }),
    setFlashlightBattery: (flashlightBattery) => set({ flashlightBattery }),
    setSpareBatteries: (spareBatteries) => set({ spareBatteries }),
    setHasWeapon: (hasWeapon) => set({ hasWeapon }),
    setAmmo: (ammo) => set({ ammo }),
    setSpeedBoostLevel: (speedBoostLevel) => set({ speedBoostLevel }),
    setMaxFlashlightBattery: (maxFlashlightBattery) =>
      set({ maxFlashlightBattery }),
    setFlashlightDrainRate: (flashlightDrainRate) =>
      set({ flashlightDrainRate }),
    setMazeData: (data) => set({ mazeData: data }),
    setCollidables: (objects) => set({ collidables: objects }),

    reset: () =>
      set({
        level: 1,
        money: 0,
        hasKey: false,
        isGameOver: false,
        isShopOpen: false,
        isHiding: false,
        stamina: 100,
        flashlightBattery: 100,
        spareBatteries: 1,
        hasWeapon: false,
        ammo: 0,
        speedBoostLevel: 0,
        maxFlashlightBattery: 100,
        flashlightDrainRate: 3.0,
        mazeData: null,
        collidables: [],
      }),
  },
}));

export default useGameStore;
